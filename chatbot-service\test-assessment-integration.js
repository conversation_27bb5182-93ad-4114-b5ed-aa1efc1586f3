/**
 * Test script untuk memverifikasi integrasi assessment dengan chatbot service
 * <PERSON><PERSON><PERSON> alur: analysis worker -> event -> chatbot service -> archive service
 */

const axios = require('axios');

// Configuration
const CHATBOT_SERVICE_URL = process.env.CHATBOT_SERVICE_URL || 'http://localhost:3004';
const ARCHIVE_SERVICE_URL = process.env.ARCHIVE_SERVICE_URL || 'http://localhost:3002';
const INTERNAL_SERVICE_KEY = process.env.INTERNAL_SERVICE_KEY || 'your-internal-service-key';

// Test data
const testUserId = 'test-user-123';
const testResultId = 'test-result-456';

/**
 * Test 1: Verify archive service endpoint with correct authentication
 */
async function testArchiveServiceEndpoint() {
  console.log('\n=== Test 1: Archive Service Endpoint ===');
  
  try {
    const response = await axios.get(
      `${ARCHIVE_SERVICE_URL}/api/archive/results/${testResultId}`,
      {
        headers: {
          'X-Internal-Service': 'true',
          'X-Service-Key': INTERNAL_SERVICE_KEY,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log('✅ Archive service endpoint accessible');
    console.log('Response status:', response.status);
    console.log('Response structure:', {
      success: response.data.success,
      hasData: !!response.data.data
    });
  } catch (error) {
    console.log('❌ Archive service endpoint error:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.message);
    console.log('Response:', error.response?.data);
  }
}

/**
 * Test 2: Test chatbot service context service methods
 */
async function testContextServiceMethods() {
  console.log('\n=== Test 2: Context Service Methods ===');
  
  try {
    // Import context service
    const ContextService = require('./src/services/contextService');
    const contextService = new ContextService();

    // Test fetchAssessmentDataByResultId
    console.log('Testing fetchAssessmentDataByResultId...');
    const assessmentData = await contextService.fetchAssessmentDataByResultId(testResultId);
    
    if (assessmentData) {
      console.log('✅ fetchAssessmentDataByResultId works');
      console.log('Assessment data structure:', Object.keys(assessmentData));
    } else {
      console.log('⚠️ fetchAssessmentDataByResultId returned null (expected if test data not exists)');
    }

    // Test getAssessmentContextByResultId
    console.log('Testing getAssessmentContextByResultId...');
    const contextData = { result_id: testResultId };
    const context = await contextService.getAssessmentContextByResultId(contextData);
    
    console.log('✅ getAssessmentContextByResultId works');
    console.log('Context length:', context.length);

  } catch (error) {
    console.log('❌ Context service error:', error.message);
  }
}

/**
 * Test 3: Simulate analysis complete event
 */
async function testAnalysisCompleteEvent() {
  console.log('\n=== Test 3: Analysis Complete Event ===');
  
  try {
    // Import assessment event handler
    const AssessmentEventHandler = require('./src/services/assessmentEventHandler');
    const eventHandler = new AssessmentEventHandler();

    // Simulate event data from analysis worker
    const eventData = {
      eventType: 'analysis.completed',
      timestamp: new Date().toISOString(),
      jobId: 'test-job-123',
      userId: testUserId,
      userEmail: '<EMAIL>',
      resultId: testResultId,
      metadata: {
        assessmentName: 'AI-Driven Talent Mapping',
        processingTime: 45000,
        retryCount: 0
      }
    };

    console.log('Simulating analysis complete event...');
    console.log('Event data:', eventData);

    // Test handleAssessmentComplete
    await eventHandler.handleAssessmentComplete(eventData);
    
    console.log('✅ Analysis complete event handled successfully');

  } catch (error) {
    console.log('❌ Analysis complete event error:', error.message);
    console.log('Stack:', error.stack);
  }
}

/**
 * Test 4: Test conversation creation with result ID
 */
async function testConversationCreation() {
  console.log('\n=== Test 4: Conversation Creation ===');
  
  try {
    const AssessmentEventHandler = require('./src/services/assessmentEventHandler');
    const eventHandler = new AssessmentEventHandler();

    // Mock assessment data
    const mockAssessmentData = {
      id: testResultId,
      user_id: testUserId,
      assessment_data: {
        riasec: { realistic: 4, investigative: 5, artistic: 3, social: 4, enterprising: 2, conventional: 3 },
        ocean: { openness: 4, conscientiousness: 5, extraversion: 3, agreeableness: 4, neuroticism: 2 },
        viaIs: { creativity: 4, perseverance: 5, honesty: 4 }
      },
      persona_profile: {
        archetype: 'The Analyst',
        personality_summary: 'Detail-oriented and analytical',
        career_recommendations: ['Data Scientist', 'Research Analyst'],
        strengths: ['Analytical thinking', 'Problem solving'],
        development_areas: ['Communication', 'Leadership']
      },
      status: 'completed'
    };

    console.log('Testing conversation creation with result ID...');
    const result = await eventHandler.createAssessmentConversationWithResultId(
      testUserId, 
      testResultId, 
      mockAssessmentData
    );

    console.log('✅ Conversation created successfully');
    console.log('Conversation ID:', result.conversation.id);
    console.log('Has welcome message:', !!result.welcomeMessage);
    console.log('Suggestions count:', result.suggestions.length);

  } catch (error) {
    console.log('❌ Conversation creation error:', error.message);
  }
}

/**
 * Test 5: Test endpoint authentication headers
 */
async function testAuthenticationHeaders() {
  console.log('\n=== Test 5: Authentication Headers ===');
  
  // Test old authentication (should fail)
  console.log('Testing old Bearer token authentication (should fail)...');
  try {
    await axios.get(
      `${ARCHIVE_SERVICE_URL}/api/archive/results/${testResultId}`,
      {
        headers: {
          'Authorization': `Bearer ${INTERNAL_SERVICE_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      }
    );
    console.log('⚠️ Old authentication still works (unexpected)');
  } catch (error) {
    console.log('✅ Old authentication properly rejected');
  }

  // Test new authentication (should work)
  console.log('Testing new X-Internal-Service authentication...');
  try {
    await axios.get(
      `${ARCHIVE_SERVICE_URL}/api/archive/results/${testResultId}`,
      {
        headers: {
          'X-Internal-Service': 'true',
          'X-Service-Key': INTERNAL_SERVICE_KEY,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      }
    );
    console.log('✅ New authentication works');
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ New authentication works (404 expected for test data)');
    } else {
      console.log('❌ New authentication failed:', error.message);
    }
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Assessment Integration Tests');
  console.log('Chatbot Service URL:', CHATBOT_SERVICE_URL);
  console.log('Archive Service URL:', ARCHIVE_SERVICE_URL);
  
  await testArchiveServiceEndpoint();
  await testContextServiceMethods();
  await testAnalysisCompleteEvent();
  await testConversationCreation();
  await testAuthenticationHeaders();
  
  console.log('\n✨ Tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testArchiveServiceEndpoint,
  testContextServiceMethods,
  testAnalysisCompleteEvent,
  testConversationCreation,
  testAuthenticationHeaders,
  runTests
};
