# Assessment Integration Fixes

## Overview
Perbaikan pada chatbot-service untuk menggunakan alur yang benar dalam integrasi dengan analysis worker dan archive service.

## Masalah yang Diperbaiki

### 1. **Endpoint yang Salah**
- **Sebelum:** `/api/archive/assessments/${assessmentId}` (tidak ada)
- **Sesudah:** `/api/archive/results/${resultId}` (sesuai dokumentasi)

### 2. **Authentication Headers yang <PERSON>**
- **Sebelum:** `Authorization: Bearer ${token}`
- **Sesudah:** `X-Internal-Service: true` dan `X-Service-Key: ${key}`

### 3. **Event Structure yang Tidak Sesuai**
- **Sebelum:** `{ user_id, assessment_id, analysis_results }`
- **Sesudah:** `{ userId, resultId, jobId, userEmail, metadata }`

### 4. **Flow yang Tidak Menggunakan Result ID**
- **Sebelum:** Menggunakan assessment_id untuk fetch data
- **Sesudah:** Menggunakan result_id dari event analysis worker

## Alur yang Benar

```mermaid
sequenceDiagram
    participant A<PERSON> as Analysis Worker
    participant RMQ as RabbitMQ
    participant CS as Chatbot Service
    participant AS as Archive Service
    
    AW->>RMQ: Publish analysis.completed event
    Note over RMQ: Event contains: userId, resultId, jobId
    
    RMQ->>CS: Deliver event to chatbot service
    CS->>AS: GET /api/archive/results/{resultId}
    Note over CS,AS: Headers: X-Internal-Service, X-Service-Key
    
    AS->>CS: Return assessment data
    CS->>CS: Create/Update conversation
    CS->>CS: Generate welcome message & suggestions
```

## Perubahan File

### 1. `src/services/contextService.js`
- ✅ Tambah `fetchAssessmentDataByResultId(resultId)`
- ✅ Tambah `getAssessmentContextByResultId(contextData)`
- ✅ Perbaiki authentication headers
- ✅ Update `buildConversationContext()` untuk support kedua method

### 2. `src/services/assessmentEventHandler.js`
- ✅ Update `handleAssessmentComplete()` untuk struktur event baru
- ✅ Tambah `findAssessmentConversationByUserId(userId)`
- ✅ Tambah `createAssessmentConversationWithResultId(userId, resultId, assessmentData)`
- ✅ Tambah `updateConversationContextWithResultId(conversationId, resultId, assessmentData)`

### 3. `src/controllers/assessmentIntegrationController.js`
- ✅ Tambah `validateAssessmentAccessByResultId(userId, resultId)`
- ✅ Tambah validasi ownership berdasarkan user_id

## Method Baru

### ContextService
```javascript
// Fetch assessment data menggunakan result ID
async fetchAssessmentDataByResultId(resultId)

// Get assessment context menggunakan result ID  
async getAssessmentContextByResultId(contextData)
```

### AssessmentEventHandler
```javascript
// Find conversation berdasarkan user ID
async findAssessmentConversationByUserId(userId)

// Create conversation dengan result ID
async createAssessmentConversationWithResultId(userId, resultId, assessmentData)

// Update conversation context dengan result ID
async updateConversationContextWithResultId(conversationId, resultId, assessmentData)
```

### AssessmentIntegrationController
```javascript
// Validate access menggunakan result ID
async validateAssessmentAccessByResultId(userId, resultId)
```

## Backward Compatibility

Semua method lama masih tersedia dengan marking `@deprecated`:
- `fetchAssessmentData()` → gunakan `fetchAssessmentDataByResultId()`
- `getAssessmentContext()` → gunakan `getAssessmentContextByResultId()`
- `validateAssessmentAccess()` → gunakan `validateAssessmentAccessByResultId()`

## Testing

Jalankan test script untuk memverifikasi perubahan:

```bash
cd chatbot-service
node test-assessment-integration.js
```

Test akan memverifikasi:
1. ✅ Archive service endpoint dengan authentication yang benar
2. ✅ Context service methods baru
3. ✅ Analysis complete event handling
4. ✅ Conversation creation dengan result ID
5. ✅ Authentication headers

## Environment Variables

Pastikan environment variables berikut sudah diset:

```env
# Archive Service
ARCHIVE_SERVICE_URL=http://localhost:3002
INTERNAL_SERVICE_KEY=your-internal-service-key

# RabbitMQ untuk event handling
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_EXCHANGE=atma_events
RABBITMQ_QUEUE=chatbot_assessment_events
RABBITMQ_ROUTING_KEY=analysis_complete

# Assessment Integration
ENABLE_ASSESSMENT_INTEGRATION=true
ENABLE_WELCOME_MESSAGES=true
```

## Monitoring

Event handling dapat dimonitor melalui:
- Logs di chatbot service
- RabbitMQ management interface
- Archive service metrics endpoint

## Next Steps

1. Deploy perubahan ke environment testing
2. Verifikasi end-to-end flow dengan analysis worker
3. Monitor logs untuk memastikan tidak ada error
4. Update dokumentasi API jika diperlukan
